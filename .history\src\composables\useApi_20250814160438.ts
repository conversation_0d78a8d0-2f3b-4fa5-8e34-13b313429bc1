/**
 * API请求组合式函数
 * 提供统一的API调用方式和状态管理
 */

import { ref } from 'vue'
import type { Ref } from 'vue'
import type { ApiResponse } from '@/utils/request'

// 请求状态接口
export interface ApiState<T> {
  data: Ref<T | null>
  loading: Ref<boolean>
  error: Ref<string | null>
  execute: () => Promise<void>
  refresh: () => Promise<void>
}

/**
 * 通用API请求Hook
 * @param apiFunction API请求函数
 * @param immediate 是否立即执行
 */
export function useApi<T>(
  apiFunction: () => Promise<ApiResponse<T>>,
  immediate = true
): ApiState<T> {
  const data = ref<T | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  const execute = async (): Promise<void> => {
    try {
      loading.value = true
      error.value = null
      
      const response = await apiFunction()
      data.value = response.data
    } catch (err: any) {
      error.value = err.message || '请求失败'
      console.error('API请求错误:', err)
    } finally {
      loading.value = false
    }
  }

  const refresh = async (): Promise<void> => {
    await execute()
  }

  // 如果需要立即执行
  if (immediate) {
    execute()
  }

  return {
    data: data as Ref<T | null>,
    loading,
    error,
    execute,
    refresh
  }
}

/**
 * 带参数的API请求Hook
 * @param apiFunction 带参数的API请求函数
 * @param params 请求参数
 * @param immediate 是否立即执行
 */
export function useApiWithParams<T, P>(
  apiFunction: (params: P) => Promise<ApiResponse<T>>,
  params: Ref<P> | P,
  immediate = true
): ApiState<T> & { updateParams: (newParams: P) => void } {
  const paramsRef = ref(params) as Ref<P>
  const data = ref<T | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  const execute = async (): Promise<void> => {
    try {
      loading.value = true
      error.value = null
      
      const response = await apiFunction(paramsRef.value)
      data.value = response.data
    } catch (err: any) {
      error.value = err.message || '请求失败'
      console.error('API请求错误:', err)
    } finally {
      loading.value = false
    }
  }

  const refresh = async (): Promise<void> => {
    await execute()
  }

  const updateParams = (newParams: P): void => {
    paramsRef.value = newParams
  }

  // 如果需要立即执行
  if (immediate) {
    execute()
  }

  return {
    data: data as Ref<T | null>,
    loading,
    error,
    execute,
    refresh,
    updateParams
  }
}

/**
 * 轮询API请求Hook
 * @param apiFunction API请求函数
 * @param interval 轮询间隔（毫秒）
 * @param immediate 是否立即执行
 */
export function usePollingApi<T>(
  apiFunction: () => Promise<ApiResponse<T>>,
  interval = 5000,
  immediate = true
): ApiState<T> & { 
  start: () => void
  stop: () => void
  isPolling: Ref<boolean>
} {
  const data = ref<T | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const isPolling = ref(false)
  let timer: number | null = null

  const execute = async (): Promise<void> => {
    try {
      loading.value = true
      error.value = null
      
      const response = await apiFunction()
      data.value = response.data
    } catch (err: any) {
      error.value = err.message || '请求失败'
      console.error('API请求错误:', err)
    } finally {
      loading.value = false
    }
  }

  const refresh = async (): Promise<void> => {
    await execute()
  }

  const start = (): void => {
    if (timer) return
    
    isPolling.value = true
    timer = setInterval(execute, interval)
  }

  const stop = (): void => {
    if (timer) {
      clearInterval(timer)
      timer = null
      isPolling.value = false
    }
  }

  // 如果需要立即执行
  if (immediate) {
    execute()
    start()
  }

  // 在Vue 3中，可以使用onUnmounted来清理
  // 这里提供cleanup函数供手动调用
  return {
    data: data as Ref<T | null>,
    loading,
    error,
    execute,
    refresh,
    start,
    stop,
    isPolling
  }
}
