{"name": "sd", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "mock": "cd mock && npm install && npm start", "mock:dev": "cd mock && npm install && npm run dev"}, "dependencies": {"@eslint/js": "^9.29.0", "@types/node": "^24.0.7", "autofit.js": "^3.2.8", "axios": "^1.11.0", "cors": "^2.8.5", "echarts": "^5.6.0", "eslint": "^9.29.0", "eslint-plugin-prettier": "^5.5.0", "eslint-plugin-vue": "^10.2.0", "express": "^5.1.0", "globals": "^16.2.0", "odometer": "^0.4.8", "prettier": "^3.5.3", "sass": "^1.89.2", "typescript-eslint": "^8.34.1", "vue": "^3.5.13", "vue-eslint-parser": "^10.1.3", "vue3-odometer": "^0.1.3", "vue3-scroll-seamless": "^1.0.6"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "typescript": "~5.7.2", "vite": "^6.1.0", "vue-tsc": "^2.2.0"}}