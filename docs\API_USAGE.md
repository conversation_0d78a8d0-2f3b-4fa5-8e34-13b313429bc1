# API配置使用说明

## 概述

本项目已配置完整的API请求环境，包括环境变量管理、HTTP请求封装、API接口定义和组合式函数等。

## 文件结构

```
src/
├── utils/
│   ├── env.ts          # 环境变量配置
│   └── request.ts      # HTTP请求工具
├── api/
│   └── index.ts        # API接口定义
├── composables/
│   └── useApi.ts       # API组合式函数
└── components/
    └── examples/
        └── ApiExample.vue  # 使用示例
```

## 环境配置

### 1. 环境变量文件

- `.env` - 开发环境配置
- `.env.production` - 生产环境配置

### 2. 配置项说明

```bash
# 环境标识
VITE_APP_ENV=development

# 应用标题
VITE_APP_TITLE=山东省旅游业智能监控平台

# API基础URL
VITE_API_BASE_URL=http://localhost:3000/api

# API超时时间（毫秒）
VITE_API_TIMEOUT=10000

# 应用版本
VITE_APP_VERSION=1.0.0
```

## 使用方法

### 1. 基础API调用

```typescript
import { useApi } from '@/composables/useApi'
import { getRealTimeData } from '@/api'

// 在组件中使用
const { data, loading, error, refresh } = useApi(getRealTimeData)
```

### 2. 带参数的API调用

```typescript
import { useApiWithParams } from '@/composables/useApi'
import { getVisitorTrend } from '@/api'

const params = ref({
  startDate: '2024-01-01',
  endDate: '2024-12-31',
  type: 'daily'
})

const { data, loading, error, updateParams } = useApiWithParams(
  getVisitorTrend,
  params
)
```

### 3. 轮询API调用

```typescript
import { usePollingApi } from '@/composables/useApi'
import { getRealTimeData } from '@/api'

const {
  data,
  loading,
  error,
  start,
  stop,
  isPolling
} = usePollingApi(getRealTimeData, 5000) // 每5秒轮询一次
```

### 4. 直接使用request工具

```typescript
import request from '@/utils/request'

// GET请求
const response = await request.get('/tourism/realtime')

// POST请求
const response = await request.post('/tourism/data', {
  name: 'test',
  value: 100
})
```

## API接口定义

所有API接口都在 `src/api/index.ts` 中定义，包括：

- `getRealTimeData()` - 获取实时数据
- `getRegionData()` - 获取地区数据
- `getHotSpots()` - 获取热门景点
- `getVisitorTrend()` - 获取游客趋势
- `getRevenueStats()` - 获取收入统计
- `getSatisfactionData()` - 获取满意度数据
- `getTrafficData()` - 获取交通数据
- `getHotelOccupancy()` - 获取酒店入住率
- `getWeatherData()` - 获取天气数据
- `getPowerSystemData()` - 获取电力系统数据

## 错误处理

HTTP请求工具已内置完整的错误处理机制：

- **请求拦截器**：自动添加token、打印请求日志
- **响应拦截器**：统一处理业务错误和HTTP错误
- **错误分类**：区分业务错误(401、403等)和网络错误

## 自定义配置

### 1. 修改API基础URL

在对应的环境变量文件中修改 `VITE_API_BASE_URL`：

```bash
# 开发环境
VITE_API_BASE_URL=http://localhost:3000/api

# 生产环境
VITE_API_BASE_URL=https://your-production-api.com/api
```

### 2. 添加新的API接口

在 `src/api/index.ts` 中添加新接口：

```typescript
export const getNewData = (): Promise<ApiResponse<NewDataType>> => {
  return request.get('/new-endpoint')
}
```

### 3. 自定义请求头

在 `src/utils/request.ts` 的请求拦截器中添加：

```typescript
config.headers['Custom-Header'] = 'custom-value'
```

## 注意事项

1. **环境变量**：所有环境变量必须以 `VITE_` 开头才能在前端访问
2. **类型安全**：所有API响应都有完整的TypeScript类型定义
3. **错误处理**：建议在组件中处理 `error` 状态，提供用户友好的错误提示
4. **性能优化**：使用轮询时记得在组件卸载时停止轮询
5. **Token管理**：token存储在localStorage中，可根据需要修改存储方式

## 示例组件

参考 `src/components/examples/ApiExample.vue` 查看完整的使用示例。
