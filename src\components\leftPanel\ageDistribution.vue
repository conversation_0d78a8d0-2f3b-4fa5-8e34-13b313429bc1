<!-- 游客年龄分布与年度接待游客比 -->
<template>
  <CPanel class="tourist-analysis">
    <template #header>地源热泵系统</template>
    <template #content>
      <div class="tourist-content">
        <!-- 上半部分：数据指标 -->
        <div class="data-indicators">
          <div class="indicator-item">
            <div class="icon-container">
              <div class="icon-circle">
                <img src="@/assets/img/dy_icon.png" alt="icon" class="icon-img" />
              </div>
            </div>
            <div class="indicator-content">
              <span class="indicator-label">地源水温度1</span>
              <span class="indicator-value">{{ heatPumpData?.temperature1 || '20.5' }}℃</span>
            </div>
          </div>
          <div class="indicator-item">
            <div class="icon-container">
              <div class="icon-circle">
                <img src="@/assets/img/dy_icon.png" alt="icon" class="icon-img" />
              </div>
            </div>
            <div class="indicator-content">
              <span class="indicator-label">地源水温度2</span>
              <span class="indicator-value">{{ heatPumpData?.temperature2 || '20.5' }}℃</span>
            </div>
          </div>
        </div>

        <!-- 下半部分：图表区域 -->
        <div class="charts-section">
          <!-- 年龄分布图表 -->
          <div class="chart-container">
            <div class="chart-title">
              <span class="title-text">换热效果趋势</span>
            </div>
            <div class="chart-wrapper">
              <CEcharts ref="ageChartRef" :option="ageOption" />
            </div>
          </div>

          <!-- 年度接待游客比图表 -->
          <div class="chart-container">
            <div class="chart-title">
              <span class="title-text">电流电压统计</span>
              <select v-model="currentType" @change="handleTypeChange" class="type-selector">
                <option value="current">电流</option>
                <option value="voltage">电压</option>
              </select>
            </div>
            <div class="chart-wrapper">
              <CEcharts ref="receptionChartRef" :option="receptionOption" />
            </div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'
import type { EChartsOption, TooltipComponentOption } from 'echarts'
import { getHeatPumpData } from '@/api/index'
import type { HeatPumpData } from '@/api/index'

const ageOption = ref<EChartsOption>({})
const receptionOption = ref<any>({})
const ageChartRef = ref()
const receptionChartRef = ref()

// 响应式数据
const heatPumpData = ref<HeatPumpData | null>(null)
const currentType = ref<'current' | 'voltage'>('current')
const loading = ref(false)

const createHeatEffectChart = (): EChartsOption => {
  const chartData = heatPumpData.value?.heatTransfer
  const xAxisData = chartData?.axis || ['7.1', '7.2', '7.3', '7.4', '7.5', '7.6', '7.7', '7.8', '7.9']
  const seriesData = chartData?.data?.[0]?.map(val => parseFloat(val)) || [18, 25, 32, 38, 45, 52, 58, 65, 72]

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line'
      },
      formatter: function (params: any) {
        const item = params[0]
        return item.name + ' : ' + item.value + ' m³/h'
      }
    } as TooltipComponentOption,
    grid: {
      left: '2%',
      right: '5%',
      top: '15%',
      bottom: '1%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        show: true,
        lineStyle: {
          width: 1,
          color: 'rgba(76, 93, 130, 1)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontSize: 12,
        color: 'rgba(201, 211, 234, 1)'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      name: 'm³/h',
      nameTextStyle: {
        color: 'rgba(201, 211, 234, 1)',
        fontSize: 12,
        padding: [0, 20, 8, 0]
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(49, 58, 86, 1)',
          type: 'solid'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontSize: 12,
        color: 'rgba(201, 211, 234, 1)'
      },
      min: 0,
      max: 100,
      interval: 20
    },
    series: [
      {
        type: 'line',
        data: seriesData,
        lineStyle: {
          width: 3,
          color: 'rgba(64, 158, 255, 1)'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(64, 158, 255, 0.6)'
            },
            {
              offset: 1,
              color: 'rgba(64, 158, 255, 0.1)'
            }
          ])
        },
        itemStyle: {
          color: 'rgba(64, 158, 255, 1)',
          borderColor: 'rgba(64, 158, 255, 1)',
          borderWidth: 2
        },
        symbol: 'circle',
        symbolSize: 6,
        smooth: true,
        emphasis: {
          itemStyle: {
            color: 'rgba(64, 158, 255, 1)',
            borderColor: '#fff',
            borderWidth: 3,
            shadowBlur: 10,
            shadowColor: 'rgba(64, 158, 255, 0.5)'
          }
        }
      }
    ]
  }
}
// 创建电流电压统计图表
const createReceptionChart = () => {
  const chartData = heatPumpData.value?.currentVoltage
  const xAxisData = chartData?.axis || ['00:00', '01:00', '02:00', '03:00', '04:00', '05:00', '06:00', '07:00', '08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00', '21:00', '22:00', '23:00']
  const legendData = chartData?.type || ['A相', 'B相', 'C相']
  const seriesData = chartData?.data || []

  return {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      top: '5%',
      right: '0',
      itemGap: 20,
      itemWidth: 15,
      itemHeight: 1,
      data: legendData,
      textStyle: {
        color: '#C5D6E6',
        fontSize: 10
      }
    },
    grid: {
      left: '8%',
      right: '5%',
      bottom: '15%',
      top: '25%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: xAxisData,
        axisLabel: {
          textStyle: {
            color: '#C5D6E6',
            fontSize: 10
          }
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(76, 93, 130, 1)'
          }
        },
        axisTick: {
          show: false
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: currentType.value === 'current' ? 'A' : 'V',
        nameTextStyle: {
          color: 'rgba(201, 211, 234, 1)',
          fontSize: 10,
          padding: [0, 20, 8, 0]
        },
        splitNumber: 3,
        splitLine: {
          lineStyle: {
            color: 'rgba(52, 71, 112, 1)',
            type: 'dashed'
          }
        },
        axisLabel: {
          textStyle: {
            color: '#C5D6E6',
            fontSize: 10
          }
        },
        axisLine: {
          show: false
        }
      }
    ],
    series: seriesData.map((data: string[], index: number) => {
      const colors = ['rgba(218, 163, 88, 1)', 'rgba(109, 128, 175, 1)', 'rgba(64, 158, 255, 1)']
      const color = colors[index] || colors[0]

      return {
        name: legendData[index] || `系列${index + 1}`,
        type: 'line',
        data: data.map(val => parseFloat(val)),
        lineStyle: {
          normal: {
            width: 2,
            color: color,
            shadowColor: color.replace('1)', '0.3)'),
            shadowBlur: 10,
            shadowOffsetY: 20
          }
        },
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: color.replace('1)', '0.3)')
                },
                {
                  offset: 1,
                  color: color.replace('1)', '0)')
                }
              ],
              false
            )
          }
        },
        itemStyle: {
          color: color
        },
        smooth: true,
        symbol: 'none'
      }
    })
  }
}

// 获取地源热泵数据
const fetchHeatPumpData = async () => {
  try {
    loading.value = true
    const response = await getHeatPumpData(currentType.value)
    console.log('地源热泵数据:', response.data)
    if (response.data && (response.data as any).data) {
      heatPumpData.value = (response.data as any).data
      // 更新图表
      ageOption.value = createHeatEffectChart()
      receptionOption.value = createReceptionChart()
    }
  } catch (error) {
    console.error('获取地源热泵数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理类型切换
const handleTypeChange = () => {
  fetchHeatPumpData()
}

onMounted(() => {
  fetchHeatPumpData()
})

onUnmounted(() => {
  // 清理资源
})
</script>

<style lang="scss" scoped>
.tourist-analysis {
  .tourist-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    // gap: 16px;
    background: url('@/assets/img/dy_bg.png') no-repeat center center;
    background-size: 100% 100%;
  }

  .data-indicators {
    display: flex;
    gap: 16px;
    height: 80px;

    .indicator-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      padding: 12px 16px;
      .icon-container {
        .icon-circle {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
        }
      }

      .indicator-content {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .indicator-label {
          font-size: 12px;
          color: rgba(201, 211, 234, 0.8);
          line-height: 1;
        }

        .indicator-value {
          font-size: 16px;
          font-weight: bold;
          color: #D9F0FF;
          line-height: 1;
          background: url('@/assets/img/dy_title.png') no-repeat center center;
          background-size: contain;
          padding: 4px 8px;
        }
      }
    }
  }

  .charts-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;

    .chart-container {
      flex: 1;
      display: flex;
      flex-direction: column;

      .chart-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 8px;
        margin-bottom: 8px;
        background: url('@/assets/img/n_samllTitle.png') no-repeat center center;
        background-size: 100% 100%;
        padding: 0 20px;
        height: 40px;
        .title-text {
          font-size: 16px;
          color: #fff;
          font-weight: 500;
        }

        .type-selector {
          background: rgba(0, 0, 0, 0.3);
          border: 1px solid rgba(64, 158, 255, 0.5);
          border-radius: 4px;
          color: #fff;
          padding: 4px 8px;
          font-size: 12px;
          outline: none;
          cursor: pointer;

          option {
            background: rgba(0, 0, 0, 0.8);
            color: #fff;
          }

          &:hover {
            border-color: rgba(64, 158, 255, 0.8);
          }

          &:focus {
            border-color: rgba(64, 158, 255, 1);
          }
        }
      }

      .chart-wrapper {
        flex: 1;
        min-height: 140px;
      }
    }
  }
}
</style>
