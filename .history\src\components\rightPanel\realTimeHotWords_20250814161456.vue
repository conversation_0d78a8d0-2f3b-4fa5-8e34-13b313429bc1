<!-- 电力系统  -->
<template>
  <CPanel>
    <template #header>
      <div class="header-content">
        <span>电力系统</span>
        <div class="dropdown" @click="toggleDropdown">
          <span>{{ selectedOption.label }}</span>
          <span class="dropdown-icon" :class="{ 'rotated': isDropdownOpen }">▼</span>
          <div v-if="isDropdownOpen" class="dropdown-menu">
            <div
              v-for="option in dropdownOptions"
              :key="option.value"
              class="dropdown-item"
              :class="{ 'active': selectedOption.value === option.value }"
              @click.stop="selectOption(option)"
            >
              {{ option.label }}
            </div>
          </div>
        </div>
      </div>
    </template>
    <template #content>
      <div class="power-system">
        <!-- 当前总有功电能显示 -->
        <div class="power-info">
          <div class="power-icon">
            <img src="@/assets/img/水泵icon.png" alt="水泵图标" />
          </div>
          <div class="power-text">
            <div class="power-label">当前总有功电能</div>
            <div class="power-value">
              <span class="value">{{ powerData?.total || '211.4' }}</span>
              <span class="unit">kw/h</span>
            </div>
          </div>
        </div>
        <!-- 图表区域 -->
        <div class="chart-container">
          <CEcharts :option="option" />
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import { onMounted, ref, onUnmounted, watch } from 'vue'
import CPanel from '../common/CPanel.vue'
import CEcharts from '../common/CEcharts.vue'
import { useApiWithParams } from '../../composables/useApi'
import { getPowerSystemData } from '../../api'

// 下拉选择器相关状态
const isDropdownOpen = ref(false)
const dropdownOptions = [
  { label: '电压', value: 'voltage' },
  { label: '电流', value: 'current' }
]
const selectedOption = ref(dropdownOptions[0])

// 图表配置
const option = ref<any>({})

// API参数
const apiParams = ref(selectedOption.value.value)

// 使用API获取电力系统数据
const { data: powerData, updateParams } = useApiWithParams(
  getPowerSystemData,
  apiParams.value,
  true
)

// 下拉选择器方法
const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value
}

const selectOption = (option: typeof dropdownOptions[0]) => {
  selectedOption.value = option
  isDropdownOpen.value = false
  // 更新API参数并重新获取数据
  apiParams.value = option.value
  updateParams(option.value)
}

// 点击外部关闭下拉菜单
const handleClickOutside = (event: MouseEvent) => {
  const dropdown = event.target as HTMLElement
  if (!dropdown.closest('.dropdown')) {
    isDropdownOpen.value = false
  }
}



const initEcharts = (data?: any) => {
  // 如果有API数据，使用API数据；否则使用默认数据
  let timeData: string[]
  let aPhaseData: number[]
  let bPhaseData: number[]
  let cPhaseData: number[]

  if (data?.unit) {
    timeData = data.unit.axis || []
    aPhaseData = data.unit.data[0]?.map((val: string) => parseFloat(val)) || []
    bPhaseData = data.unit.data[1]?.map((val: string) => parseFloat(val)) || []
    cPhaseData = data.unit.data[2]?.map((val: string) => parseFloat(val)) || []
  } else {
    // 默认数据
    timeData = ['00:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '18:00', '20:00']
    aPhaseData = [10, 15, 25, 35, 45, 55, 70, 45, 25]
    bPhaseData = [45, 55, 50, 60, 55, 40, 35, 50, 45]
    cPhaseData = [25, 35, 45, 50, 35, 25, 20, 30, 15]
  }

  // 动态设置Y轴最大值
  const maxValue = Math.max(...aPhaseData, ...bPhaseData, ...cPhaseData)
  const yAxisMax = selectedOption.value.value === 'voltage' ? 300 : Math.ceil(maxValue * 1.2)

  const options: any = {
    backgroundColor: 'transparent',
    grid: {
      top: '15%',
      left: '0',
      right: '0',
      bottom: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: timeData,
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(0, 255, 255, 0.8)',
        fontSize: 10
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: yAxisMax,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: 'rgba(0, 255, 255, 0.8)',
        fontSize: 10
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 255, 255, 0.1)',
          type: 'dashed'
        }
      }
    },
    legend: {
      data: ['A相', 'B相', 'C相'],
      bottom: '5%',
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      itemWidth: 12,
      itemHeight: 8
    },
    series: [
      {
        name: 'A相',
        type: 'line',
        data: aPhaseData,
        smooth: true,
        lineStyle: {
          color: '#00FFFF',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(0, 255, 255, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(0, 255, 255, 0.05)'
              }
            ]
          }
        },
        symbol: 'none'
      },
      {
        name: 'B相',
        type: 'line',
        data: bPhaseData,
        smooth: true,
        lineStyle: {
          color: '#FFD700',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(255, 215, 0, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(255, 215, 0, 0.05)'
              }
            ]
          }
        },
        symbol: 'none'
      },
      {
        name: 'C相',
        type: 'line',
        data: cPhaseData,
        smooth: true,
        lineStyle: {
          color: '#00FF7F',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(0, 255, 127, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(0, 255, 127, 0.05)'
              }
            ]
          }
        },
        symbol: 'none'
      }
    ],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: 'rgba(0, 255, 255, 0.5)',
      textStyle: {
        color: '#fff'
      }
    }
  }
  return options
}

// 监听数据变化，更新图表
watch(powerData, (newData) => {
  if (newData) {
    option.value = initEcharts(newData)
  }
}, { immediate: true })

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  // 初始化图表
  option.value = initEcharts()
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style lang="scss" scoped>
::v-deep .panel-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .dropdown {
    position: relative;
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    color: rgba(0, 255, 255, 0.8);
    cursor: pointer;

    .dropdown-icon {
      font-size: 12px;
      transition: transform 0.3s ease;

      &.rotated {
        transform: rotate(180deg);
      }
    }

    .dropdown-menu {
      position: absolute;
      top: 100%;
      right: 0;
      background: rgba(0, 20, 40, 0.95);
      border: 1px solid rgba(0, 255, 255, 0.3);
      border-radius: 4px;
      min-width: 80px;
      z-index: 1000;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

      .dropdown-item {
        padding: 8px 12px;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: rgba(0, 255, 255, 0.1);
          color: rgba(0, 255, 255, 0.9);
        }

        &.active {
          background: rgba(0, 255, 255, 0.2);
          color: #00FFFF;
        }

        &:first-child {
          border-radius: 4px 4px 0 0;
        }

        &:last-child {
          border-radius: 0 0 4px 4px;
        }
      }
    }

    &:hover .dropdown-icon {
      color: #00FFFF;
    }
  }
}

.power-system {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  // gap: 12px;
  background: url('@/assets/img/电力系统底框.png') no-repeat center center;
  background-size: 100% 100%;
}

.power-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 7px 16px;
  background: url('@/assets/img/dy_title.png') no-repeat center center;
  background-size: 100% 100%;
  border-radius: 8px;

  .power-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 32px;
      height: 32px;
      object-fit: contain;
    }
  }

  .power-text {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 4px;

    .power-label {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
    }

    .power-value {
      display: flex;
      align-items: baseline;
      gap: 4px;

      .value {
        font-size: 24px;
        font-weight: bold;
        color: #00FFFF;
        text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
      }

      .unit {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.6);
      }
    }
  }
}

.chart-container {
  flex: 1;
  width: 100%;
  min-height: 180px;
}
</style>
