<!-- 接待游客人数TOP5 -->
<template>
  <CPanel>
    <template #header>光伏系统</template>
    <template #content>
      <div class="photovoltaic-system">
        <!-- AC视在/无功功率对比 -->
        <div class="power-comparison">
          <div class="section-title">AC视在/无功功率对比</div>
          <div class="power-display">
            <div class="power-item">
              <div class="power-value">{{ displayData.apparent }}<span class="unit">kwh</span></div>
              <div class="power-label">AC视在功率</div>
            </div>
            <div class="center-circle">
              <div class="circle-bg"></div>
              <CEcharts ref="circleChartRef" :option="circleOption" />
              <div class="circle-content">
                <div class="circle-value">{{ displayData.total }}</div>
                <div class="circle-label">总发电量</div>
              </div>
            </div>
            <div class="power-item">
              <div class="power-value">{{ displayData.reactive }}<span class="unit">kwh</span></div>
              <div class="power-label">AC无功功率</div>
            </div>
          </div>
        </div>

        <!-- 发电量统计 -->
        <div class="power-statistics">
          <div class="section-title">发电量统计</div>
          <div class="chart-container">
            <CEcharts ref="barChartRef" :option="barOption" />
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import * as echarts from 'echarts'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'
import { getPhotovoltaicData, type PhotovoltaicData } from '@/api'

const circleChartRef = ref()
const barChartRef = ref()
const circleOption = ref<any>({})
const barOption = ref<any>({})
const photovoltaicData = ref<any>({
  data: {
    code: 0,
    msg: '',
    ok: false,
    data: {
      total: '0',
      apparent: '0',
      reactive: '0',
      electricity: {
        type: [],
        axis: [],
        data: []
      }
    }
  }
})

// 计算属性确保数据正确显示
const displayData = computed(() => {
  console.log('计算属性触发，当前数据:', photovoltaicData.value)
  
  // 打印三个圈起来的值
  const total = photovoltaicData.value.data?.data?.total || '0'
  const apparent = photovoltaicData.value.data?.data?.apparent || '0'
  const reactive = photovoltaicData.value.data?.data?.reactive || '0'
  
  console.log('圈起来的三个值:')
  console.log('total:', total)
  console.log('apparent:', apparent)
  console.log('reactive:', reactive)
  
  return {
    total,
    apparent,
    reactive
  }
})

// 获取光伏系统数据
const fetchPhotovoltaicData = async () => {
  try {
    console.log('开始获取光伏系统数据...')
    const response = await getPhotovoltaicData()
    console.log('API响应:', response)

    // 检查响应结构
    if (response && response.data) {
      console.log('设置数据:', response.data)
      photovoltaicData.value = response
      // 更新图表
      updateCharts()
    } else {
      console.log('API响应无效:', response)
    }
  } catch (error) {
    console.error('获取光伏系统数据失败:', error)
  }
}

// 更新图表
const updateCharts = () => {
  console.log('更新图表，当前数据:', photovoltaicData.value)
  if (circleChartRef.value) {
    circleOption.value = createCircleChart()
  }
  if (barChartRef.value) {
    barOption.value = createBarChart()
  }
}

// 创建圆环图配置
const createCircleChart = () => {
  const total = parseFloat(photovoltaicData.value.data?.data?.total) || 0
  const generated = total
  const remaining = Math.max(0, 100 - generated) // 假设总量为100

  return {
    series: [
      {
        type: 'pie',
        radius: ['75%', '85%'],
        center: ['50%', '50%'],
        startAngle: 90,
        data: [
          {
            value: generated,
            name: '已发电量',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#00FFFF' },
                { offset: 0.5, color: '#00CED1' },
                { offset: 1, color: '#20B2AA' }
              ])
            }
          },
          {
            value: remaining,
            name: '剩余',
            itemStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }
        ],
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        emphasis: {
          disabled: true
        },
        animation: true,
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: 0
      }
    ]
  }
}

// 创建柱状图配置
const createBarChart = () => {
  const axis = photovoltaicData.value.data?.data?.electricity?.axis || []
  const data = photovoltaicData.value.data?.data?.electricity?.data?.[0] || []

  return {
    grid: {
      left: '0',
      right: '10%',
      bottom: '15%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: axis,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#C5D6E6',
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      max: Math.max(...data.map((d: any) => parseFloat(d) || 0), 80),
      axisLine: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(52, 71, 112, 0.5)',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#C5D6E6',
        fontSize: 10
      },
      axisTick: {
        show: false
      }
    },
    series: [
      {
        type: 'bar',
        barWidth: '60%',
        data: data.map((d: any) => parseFloat(d) || 0),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 0, color: 'rgba(0, 255, 255, 0.1)' },
            { offset: 1, color: 'rgba(0, 255, 255, 0.8)' }
          ])
        }
      }
    ]
  }
}

onMounted(async () => {
  // 获取数据并初始化图表
  await fetchPhotovoltaicData()

  // 设置定时刷新（每30秒刷新一次）
  setInterval(fetchPhotovoltaicData, 30000)
})
</script>
<style lang="scss" scoped>
.photovoltaic-system {
  width: 472px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
  background: url('@/assets/img/光伏系统底框.png') no-repeat center center;
  background-size: 100% 100%;

  .main-title {
    position: relative;
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    background: url('@/assets/img/small.png') no-repeat center center;
    background-size: 100% 100%;
    font-size: 20px;
    letter-spacing: 1px;
    color: #fff;
    padding-left: 20px;
  }

  .power-comparison {
    position: relative;
    // margin-bottom: 8px;

    .section-title {
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
      background: url('@/assets/img/n_samllTitle.png') no-repeat center center;
      background-size: 100% 100%;
      font-size: 16px;
      color: #fff;
      padding-left: 20px;
      margin-bottom: 8px;
    }

    .power-display {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;
      background: url('@/assets/img/gf_bg.png') no-repeat center center;
      background-size: 100% 100%;

      .power-item {
        text-align: center;
        color: #fff;

        .power-value {
          font-size: 20px;
          font-weight: bold;
          color: #00FFFF;
          margin-bottom: 4px;

          .unit {
            font-size: 14px;
            color: #C5D6E6;
          }
        }

        .power-label {
          font-size: 14px;
          color: #C5D6E6;
        }
      }

      .center-circle {
        width: 140px;
        height: 140px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        .circle-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          // // background: url('@/assets/img/circle_bg.svg') no-repeat center center;
          // background-size: contain;
          // z-index: 1;
        }

        .circle-content {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          text-align: center;
          z-index: 3;
          color: #fff;

          .circle-value {
            font-size: 24px;
            font-weight: bold;
            color: #00FFFF;
            margin-bottom: 2px;
          }

          .circle-label {
            font-size: 14px;
            color: #C5D6E6;
          }
        }

        :deep(.echarts) {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 2;
        }
      }
    }
  }



  .power-statistics {
    flex: 1;

    .section-title {
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
      background: url('@/assets/img/n_samllTitle.png') no-repeat center center;
      background-size: 100% 100%;
      font-size: 16px;
      color: #fff;
      padding-left: 20px;
      margin-bottom: 8px;
    }

    .chart-container {
      height: 120px;
      width: 100%;
    }
  }
}
</style>
