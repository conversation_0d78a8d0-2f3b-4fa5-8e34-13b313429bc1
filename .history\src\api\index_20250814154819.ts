/**
 * API接口统一管理
 */

import request from '@/utils/request'
import type { ApiResponse } from '@/utils/request'

// 电力系统数据接口
export interface PowerSystemData {
  total: string
  unit: {
    type: string[]
    axis: string[]
    data: string[][]
  }
}

/**
 * 获取电力系统数据
 * @param type 电流: current 电压: voltage
 */
export const getPowerSystemData = (type: string): Promise<ApiResponse<PowerSystemData>> => {
  return request.get(`/cabin/power/${type}`)
}

// 导出所有API
export default {
  getPowerSystemData
}
