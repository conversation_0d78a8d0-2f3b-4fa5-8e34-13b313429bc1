# API配置快速开始指南

## 🎉 配置完成

您的项目已经成功配置了完整的API环境！包括：

✅ **axios HTTP请求库**  
✅ **环境变量配置**  
✅ **统一的API接口管理**  
✅ **组合式函数封装**  
✅ **完整的错误处理**  
✅ **模拟API服务器**  

## 🚀 立即开始使用

### 1. 启动模拟API服务器（可选）

```bash
# 启动模拟服务器用于测试
pnpm mock

# 或者使用开发模式（自动重启）
pnpm mock:dev
```

模拟服务器将在 `http://localhost:3000` 启动

### 2. 在组件中使用API

```vue
<template>
  <div>
    <div v-if="loading">加载中...</div>
    <div v-else-if="error">{{ error }}</div>
    <div v-else-if="data">
      <p>总游客数: {{ data.totalVisitors }}</p>
      <p>今日游客数: {{ data.todayVisitors }}</p>
    </div>
    <button @click="refresh">刷新数据</button>
  </div>
</template>

<script setup lang="ts">
import { useApi } from '@/composables/useApi'
import { getRealTimeData } from '@/api'

const { data, loading, error, refresh } = useApi(getRealTimeData)
</script>
```

### 3. 配置您的API地址

修改 `.env` 文件中的API地址：

```bash
# 开发环境 - 使用模拟服务器
VITE_API_BASE_URL=http://localhost:3000/api

# 或者使用您的真实API地址
VITE_API_BASE_URL=https://your-api-domain.com/api
```

## 📁 重要文件说明

| 文件路径 | 说明 |
|---------|------|
| `src/utils/request.ts` | HTTP请求工具，基于axios封装 |
| `src/api/index.ts` | API接口定义 |
| `src/composables/useApi.ts` | Vue组合式函数 |
| `src/utils/env.ts` | 环境变量工具 |
| `.env` | 开发环境配置 |
| `.env.production` | 生产环境配置 |
| `mock/server.js` | 模拟API服务器 |

## 🔧 常用API方法

```typescript
// 1. 基础用法
const { data, loading, error } = useApi(getRealTimeData)

// 2. 带参数
const { data, loading, error, updateParams } = useApiWithParams(
  getVisitorTrend,
  { startDate: '2024-01-01', endDate: '2024-12-31' }
)

// 3. 轮询数据
const { data, start, stop, isPolling } = usePollingApi(
  getRealTimeData, 
  5000 // 每5秒轮询
)

// 4. 直接调用
const response = await request.get('/tourism/realtime')
```

## 🌐 可用的API接口

- `getRealTimeData()` - 实时旅游数据
- `getRegionData()` - 地区数据
- `getHotSpots()` - 热门景点
- `getVisitorTrend()` - 游客趋势
- `getRevenueStats()` - 收入统计
- `getSatisfactionData()` - 满意度数据
- `getTrafficData()` - 交通数据
- `getHotelOccupancy()` - 酒店入住率
- `getWeatherData()` - 天气数据
- `getPowerSystemData()` - 电力系统数据

## 📖 详细文档

查看 `docs/API_USAGE.md` 获取完整的使用文档和高级配置。

## 🎯 下一步

1. **替换模拟数据**：将 `.env` 中的API地址改为您的真实API
2. **添加新接口**：在 `src/api/index.ts` 中添加新的API接口
3. **自定义错误处理**：在 `src/utils/request.ts` 中自定义错误处理逻辑
4. **添加认证**：在请求拦截器中添加token或其他认证信息

## 💡 提示

- 所有环境变量必须以 `VITE_` 开头
- 使用TypeScript获得完整的类型提示
- 在组件卸载时记得停止轮询
- 查看 `src/components/examples/ApiExample.vue` 获取完整示例

---

🎉 **恭喜！您现在可以开始调用API接口了！**
