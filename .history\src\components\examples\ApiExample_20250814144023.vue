<!--
  API使用示例组件
  展示如何在组件中使用API配置
-->
<template>
  <div class="api-example">
    <h3>API使用示例</h3>
    
    <!-- 实时数据展示 -->
    <div class="data-section">
      <h4>实时数据</h4>
      <div v-if="realTimeLoading" class="loading">加载中...</div>
      <div v-else-if="realTimeError" class="error">{{ realTimeError }}</div>
      <div v-else-if="realTimeData" class="data">
        <p>总游客数: {{ realTimeData.totalVisitors }}</p>
        <p>今日游客数: {{ realTimeData.todayVisitors }}</p>
        <p>收入: {{ realTimeData.revenue }}</p>
        <p>满意度: {{ realTimeData.satisfaction }}</p>
      </div>
      <button @click="refreshRealTimeData">刷新数据</button>
    </div>

    <!-- 地区数据展示 -->
    <div class="data-section">
      <h4>地区数据</h4>
      <div v-if="regionLoading" class="loading">加载中...</div>
      <div v-else-if="regionError" class="error">{{ regionError }}</div>
      <div v-else-if="regionData" class="data">
        <div v-for="region in regionData" :key="region.region" class="region-item">
          <span>{{ region.region }}: </span>
          <span>游客 {{ region.visitors }}, </span>
          <span>收入 {{ region.revenue }}, </span>
          <span>增长 {{ region.growth }}%</span>
        </div>
      </div>
    </div>

    <!-- 轮询数据展示 -->
    <div class="data-section">
      <h4>轮询数据 (每5秒更新)</h4>
      <div class="polling-controls">
        <button @click="startPolling" :disabled="isPolling">开始轮询</button>
        <button @click="stopPolling" :disabled="!isPolling">停止轮询</button>
        <span v-if="isPolling" class="polling-status">正在轮询...</span>
      </div>
      <div v-if="pollingLoading" class="loading">加载中...</div>
      <div v-else-if="pollingError" class="error">{{ pollingError }}</div>
      <div v-else-if="pollingData" class="data">
        <p>轮询数据: {{ JSON.stringify(pollingData) }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onUnmounted } from 'vue'
import { useApi, usePollingApi } from '@/composables/useApi'
import { getRealTimeData, getRegionData } from '@/api'
import type { RealTimeData, RegionData } from '@/api'

// 使用实时数据API
const {
  data: realTimeData,
  loading: realTimeLoading,
  error: realTimeError,
  refresh: refreshRealTimeData
} = useApi<RealTimeData>(getRealTimeData)

// 使用地区数据API
const {
  data: regionData,
  loading: regionLoading,
  error: regionError
} = useApi<RegionData[]>(getRegionData)

// 使用轮询API
const {
  data: pollingData,
  loading: pollingLoading,
  error: pollingError,
  start: startPolling,
  stop: stopPolling,
  isPolling
} = usePollingApi<RealTimeData>(getRealTimeData, 5000, false)

// 组件卸载时停止轮询
onUnmounted(() => {
  stopPolling()
})
</script>

<style lang="scss" scoped>
.api-example {
  padding: 20px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  color: #fff;

  h3, h4 {
    color: #00FFFF;
    margin-bottom: 16px;
  }

  .data-section {
    margin-bottom: 24px;
    padding: 16px;
    background: rgba(0, 255, 255, 0.1);
    border-radius: 6px;
    border: 1px solid rgba(0, 255, 255, 0.3);

    .loading {
      color: #FFD700;
      font-style: italic;
    }

    .error {
      color: #FF6B6B;
      font-weight: bold;
    }

    .data {
      color: #fff;
      
      p {
        margin: 8px 0;
      }

      .region-item {
        display: block;
        margin: 8px 0;
        padding: 8px;
        background: rgba(0, 255, 255, 0.05);
        border-radius: 4px;
      }
    }

    button {
      margin-top: 12px;
      margin-right: 8px;
      padding: 8px 16px;
      background: linear-gradient(45deg, #00FFFF, #0080FF);
      border: none;
      border-radius: 4px;
      color: #000;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 255, 255, 0.4);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .polling-controls {
      margin-bottom: 12px;

      .polling-status {
        color: #00FF7F;
        font-weight: bold;
        margin-left: 12px;
      }
    }
  }
}
</style>
