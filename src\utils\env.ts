/**
 * 环境变量配置
 */

// 获取环境变量
export const getEnv = (): string => {
  return import.meta.env.VITE_APP_ENV || 'development'
}

// 获取API基础URL
export const getApiBaseUrl = (): string => {
  return import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api'
}

// 获取API超时时间
export const getApiTimeout = (): number => {
  return Number(import.meta.env.VITE_API_TIMEOUT) || 10000
}

// 获取应用标题
export const getAppTitle = (): string => {
  return import.meta.env.VITE_APP_TITLE || '山东省旅游业智能监控平台'
}

// 获取应用版本
export const getAppVersion = (): string => {
  return import.meta.env.VITE_APP_VERSION || '1.0.0'
}

// 判断是否为开发环境
export const isDev = (): boolean => {
  return getEnv() === 'development'
}

// 判断是否为生产环境
export const isProd = (): boolean => {
  return getEnv() === 'production'
}

// 环境配置对象
export const envConfig = {
  env: getEnv(),
  apiBaseUrl: getApiBaseUrl(),
  apiTimeout: getApiTimeout(),
  appTitle: getAppTitle(),
  appVersion: getAppVersion(),
  isDev: isDev(),
  isProd: isProd()
}
