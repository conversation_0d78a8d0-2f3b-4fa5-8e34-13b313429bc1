<!-- 电力系统  -->
<template>
  <CPanel>
    <template #header>
      <div class="header-content">
        <span>电力系统</span>
        <div class="dropdown" @click="toggleDropdown">
          <span>{{ selectedOption.label }}</span>
          <span class="dropdown-icon" :class="{ 'rotated': isDropdownOpen }">▼</span>
          <div v-if="isDropdownOpen" class="dropdown-menu" @click.stop>
            <div v-for="option in dropdownOptions" :key="option.value" class="dropdown-item"
              :class="{ 'active': selectedOption.value === option.value }" @click="selectOption(option)">
              {{ option.label }}
            </div>
          </div>
        </div>
      </div>
    </template>
    <template #content>
      <div class="power-system">
        <!-- 当前总有功电能显示 -->
        <div class="power-info">
          <div class="power-icon">
            <img src="@/assets/img/电力icon.png" alt="水泵图标" />
          </div>
          <div class="power-text">
            <div class="power-label">当前总有功电能</div>
            <div class="power-value">
              <span class="value">{{ powerData?.total || '211.4' }}</span>
              <span class="unit">kw/h</span>
            </div>
          </div>
        </div>
        <!-- 图表区域 -->
        <div class="chart-container">
          <CEcharts v-if="option" :option="option" />
          <div v-else class="no-data">
            <span>暂无数据</span>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import { onMounted, ref, onUnmounted, watch, nextTick } from 'vue'
import CPanel from '../common/CPanel.vue'
import CEcharts from '../common/CEcharts.vue'
import { useApiWithParams } from '../../composables/useApi'
import { getPowerSystemData } from '../../api'

// 下拉选择器相关状态
const isDropdownOpen = ref(false)
const dropdownOptions = [
  { label: '电压', value: 'voltage' },
  { label: '电流', value: 'current' }
]
const selectedOption = ref(dropdownOptions[0]) // 默认选择电压

// 图表配置
const option = ref<any>(null)

// API参数
const apiParams = ref(selectedOption.value.value) // 默认使用电压参数

// 使用API获取电力系统数据
const { data: powerData, updateParams, execute } = useApiWithParams(
  getPowerSystemData,
  apiParams, // 传递ref引用，不是值
  true
)

// 添加调试信息
console.log('Component initialized, apiParams:', apiParams.value)
console.log('Component initialized, powerData:', powerData)
console.log('Initial option value:', option.value)

// 下拉选择器方法
const toggleDropdown = () => {
  console.log('Toggle dropdown clicked')
  isDropdownOpen.value = !isDropdownOpen.value
}

const selectOption = (option: typeof dropdownOptions[0]) => {
  console.log('Selecting option:', option.value, 'Previous option:', selectedOption.value.value)

  // 先关闭下拉菜单
  isDropdownOpen.value = false

  // 更新选中的选项
  selectedOption.value = option

  // 更新API参数并重新获取数据
  apiParams.value = option.value
  updateParams(option.value)

  // 手动执行API调用以确保数据刷新
  execute()

  console.log('Option selected:', option.value, 'New apiParams:', apiParams.value)
  console.log('Executing API call for:', option.value)
}

// 点击外部关闭下拉菜单
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  console.log('Click outside detected, target:', target.className)

  // 检查点击的元素是否在下拉菜单内
  if (!target.closest('.dropdown')) {
    console.log('Closing dropdown - clicked outside')
    isDropdownOpen.value = false
  }
}

const initEcharts = (data: any) => {
  console.log('initEcharts received data:', data)
  console.log('Data type:', typeof data)
  console.log('Data keys:', Object.keys(data || {}))

  // 只在有数据时才创建图表配置
  if (!data?.unit) {
    console.log('No unit data found, returning null')
    console.log('Data structure:', JSON.stringify(data, null, 2))
    return null
  }

  console.log('Unit data found:', data.unit)
  console.log('Unit keys:', Object.keys(data.unit))

  const timeData = data.unit.axis || []
  const aPhaseData = data.unit.data[0]?.map((val: string) => parseFloat(val)) || []
  const bPhaseData = data.unit.data[1]?.map((val: string) => parseFloat(val)) || []
  const cPhaseData = data.unit.data[2]?.map((val: string) => parseFloat(val)) || []

  console.log('Processed data:', {
    timeData: timeData.length,
    aPhaseData: aPhaseData.length,
    bPhaseData: bPhaseData.length,
    cPhaseData: cPhaseData.length
  })
  console.log('Sample data:', {
    timeData: timeData.slice(0, 3),
    aPhaseData: aPhaseData.slice(0, 3),
    bPhaseData: bPhaseData.slice(0, 3),
    cPhaseData: cPhaseData.slice(0, 3)
  })

  // 动态设置Y轴最大值
  const maxValue = Math.max(...aPhaseData, ...bPhaseData, ...cPhaseData)
  const yAxisMax = selectedOption.value.value === 'voltage' ? 300 : Math.ceil(maxValue * 1.2)

  console.log('Y-axis max value:', yAxisMax, 'Selected option:', selectedOption.value.value)

  const options: any = {
    backgroundColor: 'transparent',
    grid: {
      top: '5%',
      left: '0',
      right: '0',
      bottom: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: timeData,
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(0, 255, 255, 0.8)',
        fontSize: 10
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: yAxisMax,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: 'rgba(0, 255, 255, 0.8)',
        fontSize: 10
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 255, 255, 0.1)',
          type: 'dashed'
        }
      }
    },
    legend: {
      data: ['A相', 'B相', 'C相'],
      bottom: '5%',
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      itemWidth: 12,
      itemHeight: 8
    },
    series: [
      {
        name: 'A相',
        type: 'line',
        data: aPhaseData,
        smooth: true,
        lineStyle: {
          color: '#00FFFF',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(0, 255, 255, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(0, 255, 255, 0.05)'
              }
            ]
          }
        },
        symbol: 'none'
      },
      {
        name: 'B相',
        type: 'line',
        data: bPhaseData,
        smooth: true,
        lineStyle: {
          color: '#FFD700',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(255, 215, 0, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(255, 215, 0, 0.05)'
              }
            ]
          }
        },
        symbol: 'none'
      },
      {
        name: 'C相',
        type: 'line',
        data: cPhaseData,
        smooth: true,
        lineStyle: {
          color: '#00FF7F',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(0, 255, 127, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(0, 255, 127, 0.05)'
              }
            ]
          }
        },
        symbol: 'none'
      }
    ],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: 'rgba(0, 255, 255, 0.5)',
      textStyle: {
        color: '#fff'
      }
    }
  }

  console.log('Generated chart options:', options)
  return options
}

// 监听数据变化，更新图表
watch(powerData, async (newData) => {
  console.log('powerData changed:', newData)
  console.log('Current selected option:', selectedOption.value.value)

  if (newData) {
    // 正确访问API返回的数据结构 - 传递 newData.data 而不是 newData
    console.log('Processing new data:', newData)
    console.log('Extracted data payload:', (newData as any).data)

    const chartOption = initEcharts((newData as any).data) // 传递 newData.data
    console.log('Generated chart option:', chartOption)

    if (chartOption) {
      await nextTick()
      option.value = chartOption
      console.log('Chart option updated successfully:', option.value)
      console.log('Chart will display for:', selectedOption.value.value)
    } else {
      console.log('No chart option generated, setting to null')
      option.value = null
    }
  } else {
    option.value = null
    console.log('Chart option set to null (no data)')
  }
}, { immediate: true, deep: true })

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style lang="scss" scoped>
::v-deep .panel-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .dropdown {
    position: relative;
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    color: rgba(0, 255, 255, 0.8);
    cursor: pointer;

    .dropdown-icon {
      font-size: 12px;
      transition: transform 0.3s ease;

      &.rotated {
        transform: rotate(180deg);
      }
    }

    .dropdown-menu {
      position: absolute;
      top: 100%;
      right: 0;
      background: rgba(0, 20, 40, 0.95);
      border: 1px solid rgba(0, 255, 255, 0.3);
      border-radius: 4px;
      min-width: 80px;
      z-index: 1000;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

      .dropdown-item {
        padding: 8px 12px;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: rgba(0, 255, 255, 0.1);
          color: rgba(0, 255, 255, 0.9);
        }

        &.active {
          background: rgba(0, 255, 255, 0.2);
          color: #00FFFF;
        }

        &:first-child {
          border-radius: 4px 4px 0 0;
        }

        &:last-child {
          border-radius: 0 0 4px 4px;
        }
      }
    }
  }
}

.power-system {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  // gap: 12px;
  background: url('@/assets/img/电力系统底框.png') no-repeat center center;
  background-size: 100% 100%;
}

.power-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 20px 16px;
  position: relative;

  .power-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px dashed rgba(0, 255, 255, 0.6);
    border-radius: 50%;
    background: rgba(0, 255, 255, 0.1);

    img {
      width: 28px;
      height: 28px;
      object-fit: contain;
      filter: brightness(0) saturate(100%) invert(100%) sepia(100%) saturate(1000%) hue-rotate(180deg);
    }
  }

  .power-text {
    display: flex;
    align-items: center;
    gap: 16px;

    .power-label {
      font-size: 16px;
      color: #ffffff;
      font-weight: 500;
      white-space: nowrap;
    }

    .power-value {
      display: flex;
      align-items: baseline;
      gap: 6px;
      padding: 8px 16px 8px 12px;
      background: url('@/assets/img/dy_title.png') no-repeat center center;
      background-size: 100% 100%;
      position: relative;

      .value {
        font-size: 28px;
        font-weight: bold;
        color: #ffffff;
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
      }

      .unit {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
        font-weight: 500;
      }
    }
  }
}

.chart-container {
  flex: 1;
  width: 100%;
  min-height: 180px;

  .no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: rgba(255, 255, 255, 0.5);
    font-size: 14px;
  }
}
</style>
