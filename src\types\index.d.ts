//如果第三方包偷懒没有ts版本，就需要进行以下设置
declare module 'vue3-scroll-seamless' {
  export { vue3ScrollSeamless }
}
declare module 'vue3-odometer' {
  export { Vue3Odometer }
}

// 环境变量类型声明
interface ImportMetaEnv {
  readonly VITE_APP_ENV: string
  readonly VITE_APP_TITLE: string
  readonly VITE_API_BASE_URL: string
  readonly VITE_API_TIMEOUT: string
  readonly VITE_APP_VERSION: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
